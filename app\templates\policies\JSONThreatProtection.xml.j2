<JSONThreatProtection name="{{ name }}">
<DisplayName>{{ params.display_name | default(name) }}</DisplayName>
  {% if params.source %}<Source>{{ params.source }}</Source>{% endif %}
  {% if params.container_depth is defined %}<ContainerDepth>{{ params.container_depth }}</ContainerDepth>{% endif %}
  {% if params.array_elements is defined %}<ArrayElementCount>{{ params.array_elements }}</ArrayElementCount>{% endif %}
  {% if params.object_entry_count is defined %}<ObjectEntryCount>{{ params.object_entry_count }}</ObjectEntryCount>{% endif %}
  {% if params.object_entry_name_length is defined %}<ObjectEntryNameLength>{{ params.object_entry_name_length }}</ObjectEntryNameLength>{% endif %}
  {% if params.source is defined %}<Source>{{ params.source }}</Source>{% endif %}
  {% if params.string_value_length is defined %}<StringValueLength>{{ params.string_value_length }}</StringValueLength>{% endif %}
  {% if params.properties is defined %}<Properties>{{ params.properties }}</Properties>{% endif %}
</JSONThreatProtection>