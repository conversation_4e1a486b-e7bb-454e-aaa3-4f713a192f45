# app/services/target_builder.py
from __future__ import annotations

from dataclasses import dataclass
from pathlib import Path
from typing import Any, Dict, Optional
from urllib.parse import urlparse

import jinja2
import structlog

from ..core.errors import ValidationError
from ..core.settings import settings
from ..config.loader import ConfigLoader  # <-- NEW

log = structlog.get_logger(__name__)


@dataclass(frozen=True)
class TargetEndpointSpec:
    name: str
    url: str
    connect_ms: int
    read_ms: int
    ssl_enabled: bool
    sni_hostname: Optional[str] = None


class TargetBuilder:
    """
    Env-aware TargetEndpoint builder.

    Usage patterns:
      A) Back-compat (as today):
         build_target_endpoint_xml(system_cfg)

      B) Env-aware (recommended):
         build_target_endpoint_xml(system_name="payments")
         # internally loads config/env/<ENV>/payments.yaml and merges it
    """

    def __init__(self, templates_dir: Optional[str] = None, loader: Optional[ConfigLoader] = None) -> None:
        tdir = templates_dir or settings.templates_dir
        base = Path(tdir).joinpath("endpoints").resolve()
        if not base.exists():
            raise ValidationError(
                f"Target templates directory not found: {base}",
                details={"templates_dir": str(base)},
            )
        self._env = jinja2.Environment(
            loader=jinja2.FileSystemLoader(str(base)),
            autoescape=False,
            trim_blocks=True,
            lstrip_blocks=True,
        )
        self._env.undefined = jinja2.StrictUndefined
        self._templates_root = base

        # Optional shared loader (can be injected from orchestrator for testability)
        self._loader = loader or ConfigLoader()

    def build_target_endpoint_xml(
        self,
        system_cfg: Optional[Dict[str, Any]] = None,
        *,
        system_name: Optional[str] = None,
        endpoint_name: Optional[str] = None,
    ) -> str:
        """
        Build TargetEndpoint XML.

        Priority of inputs:
          - If system_name is provided -> load env-aware 'effective' config via ConfigLoader
          - Else use provided system_cfg as before

        Expects 'target' section present in the chosen config.
        """
        if system_name:
            loaded = self._loader.load(system_name=system_name)
            cfg = (loaded.effective or {}).get("target") or {}
            log.info(
                "target_config_resolved",
                source="loader",
                system=system_name,
                env=self._loader._env_name,  # safe; just the string name
                has_env_overrides=bool(loaded.env_path),
            )
        else:
            cfg = (system_cfg or {}).get("target") or {}
            log.info("target_config_resolved", source="system_cfg", system_name=system_name or "<none>")

        # --- Validate and map to spec ---
        name = (endpoint_name or cfg.get("name") or "default").strip()
        url = str(cfg.get("baseurl") or "").strip()
        if not url:
            raise ValidationError("target.baseurl is required")

        parsed = urlparse(url)
        if not parsed.scheme or not parsed.netloc:
            raise ValidationError("target.baseurl must be an absolute HTTP(S) URL", details={"url": url})

        connect_ms = int(cfg.get("connect_timeout_ms") or 4000)
        read_ms = int(cfg.get("read_timeout_ms") or 30000)

        explicit_ssl_enabled = cfg.get("enabled")
        ssl_enabled = bool(explicit_ssl_enabled) if explicit_ssl_enabled is not None else (parsed.scheme == "https")

        sni_hostname = cfg.get("sni_hostname")
        spec = TargetEndpointSpec(
            name=name,
            url=url,
            connect_ms=connect_ms,
            read_ms=read_ms,
            ssl_enabled=ssl_enabled,
            sni_hostname=sni_hostname.strip() if isinstance(sni_hostname, str) and sni_hostname.strip() else None,
        )

        xml = self._render("target.xml.j2", spec)
        log.info(
            "target_endpoint_built",
            name=spec.name,
            url=spec.url,
            connect_ms=spec.connect_ms,
            read_ms=spec.read_ms,
            ssl=spec.ssl_enabled,
            sni=bool(spec.sni_hostname),
        )
        return xml.strip()

    # -------- internal --------

    def _render(self, template_name: str, spec: TargetEndpointSpec) -> str:
        try:
            tmpl = self._env.get_template(template_name)
        except jinja2.TemplateNotFound as e:
            raise ValidationError(
                "Target template not found",
                details={"template": template_name, "templates_root": str(self._templates_root)},
            ) from e

        try:
            xml: str = tmpl.render(
                name=spec.name,
                url=spec.url,
                timeouts={"connect_ms": spec.connect_ms, "read_ms": spec.read_ms},
                ssl={"enabled": spec.ssl_enabled, "sni_hostname": spec.sni_hostname},
            )
        except jinja2.UndefinedError as e:
            raise ValidationError(
                "Missing variable while rendering Target template",
                details={"template": template_name, "error": str(e)},
            ) from e

        return xml.strip()