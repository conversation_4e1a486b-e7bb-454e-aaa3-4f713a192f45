"""
Conditional Policy Attachment System
===================================
Dynamic policy attachment based on conditions like environment, features, 
API characteristics, and runtime context.
"""

from typing import Any, Dict, List, Optional, Tuple
import structlog

from .feature_flags import get_feature_evaluator

log = structlog.get_logger(__name__)


class PolicyCondition:
    """Represents a condition for policy attachment"""
    
    def __init__(self, condition_type: str, **kwargs):
        self.type = condition_type
        self.params = kwargs
    
    def evaluate(self, context: Dict[str, Any]) -> bool:
        """Evaluate the condition against the given context"""
        if self.type == "environment":
            required_envs = self.params.get("environments", [])
            current_env = context.get("env_name", "test")
            return current_env in required_envs
        
        elif self.type == "feature_flag":
            flag_name = self.params.get("flag")
            if not flag_name:
                return False

            evaluator = get_feature_evaluator(
                context.get("env_name", "test"),
                context.get("system_name")
            )

            # Create feature flag context with proper content_type mapping
            flag_context = dict(context)
            if "content_types" in context and context["content_types"]:
                # Map content_types to content_type for feature flag evaluation
                flag_context["content_type"] = context["content_types"][0]

            return evaluator.is_enabled(flag_name, flag_context)
        
        elif self.type == "api_method_count":
            methods = context.get("methods", [])
            operator = self.params.get("operator", "greater_than")
            threshold = self.params.get("value", 0)
            
            if operator == "greater_than":
                return len(methods) > threshold
            elif operator == "less_than":
                return len(methods) < threshold
            elif operator == "equals":
                return len(methods) == threshold
        
        elif self.type == "has_security":
            return bool(context.get("security_policies"))
        
        elif self.type == "content_type":
            content_types = context.get("content_types", [])
            required_types = self.params.get("types", [])
            return any(ct in content_types for ct in required_types)
        
        elif self.type == "system":
            required_systems = self.params.get("systems", [])
            current_system = context.get("system_name")
            return current_system in required_systems
        
        elif self.type == "custom":
            # Custom condition evaluation using lambda or function
            evaluator = self.params.get("evaluator")
            if callable(evaluator):
                return evaluator(context)
        
        return False


class ConditionalPolicy:
    """Represents a policy with attachment conditions"""
    
    def __init__(self, policy_name: str, display_name: Optional[str] = None,
                 conditions: Optional[List[PolicyCondition]] = None,
                 params: Optional[Dict[str, Any]] = None,
                 priority: int = 100):
        self.policy_name = policy_name
        self.display_name = display_name
        self.conditions = conditions or []
        self.params = params or {}
        self.priority = priority  # Lower number = higher priority
    
    def should_attach(self, context: Dict[str, Any]) -> bool:
        """Check if this policy should be attached based on conditions"""
        if not self.conditions:
            return True  # No conditions = always attach
        
        # All conditions must be true (AND logic)
        for condition in self.conditions:
            if not condition.evaluate(context):
                return False
        
        return True
    
    def to_policy_entry(self) -> Dict[str, Any]:
        """Convert to policy entry format for rendering"""
        entry = {"policy": self.policy_name}
        
        if self.display_name:
            entry["display_name"] = self.display_name
        
        if self.params:
            entry["params"] = self.params
        
        return entry


class ConditionalPolicyAttacher:
    """Manages conditional policy attachment"""
    
    def __init__(self):
        self.conditional_policies: List[ConditionalPolicy] = []
        self._load_conditional_policies()
    
    def _load_conditional_policies(self):
        """Load conditional policies from configuration"""
        # This would typically load from a configuration file
        # For now, we'll define some common conditional policies
        
        # Security policies based on environment
        self.conditional_policies.extend([
            ConditionalPolicy(
                policy_name="JSONThreatProtection",
                display_name="JTP-JSONThreatProtection",
                conditions=[
                    PolicyCondition("feature_flag", flag="json_threat_protection"),
                    PolicyCondition("content_type", types=["application/json"])
                ],
                priority=10
            ),
            
            ConditionalPolicy(
                policy_name="XMLThreatProtection", 
                display_name="XTP-XMLThreatProtection",
                conditions=[
                    PolicyCondition("feature_flag", flag="xml_threat_protection"),
                    PolicyCondition("content_type", types=["application/xml", "text/xml"])
                ],
                priority=10
            ),
            
            ConditionalPolicy(
                policy_name="RegularExpressionProtection",
                display_name="REP-RegularExpressionProtection",
                conditions=[
                    PolicyCondition("environment", environments=["prod", "staging"]),
                    PolicyCondition("feature_flag", flag="advanced_security")
                ],
                priority=5
            ),
            
            # Caching policies for high-traffic APIs
            ConditionalPolicy(
                policy_name="ResponseCache",
                display_name="RC-ResponseCache",
                conditions=[
                    PolicyCondition("feature_flag", flag="smart_caching"),
                    PolicyCondition("api_method_count", operator="greater_than", value=5)
                ],
                priority=50
            ),
            
            # Enhanced monitoring for production
            ConditionalPolicy(
                policy_name="FlowCallout",
                display_name="FC-EnhancedMonitoring",
                conditions=[
                    PolicyCondition("environment", environments=["prod"]),
                    PolicyCondition("feature_flag", flag="enhanced_monitoring")
                ],
                params={"shared_flow": "enhanced-monitoring"},
                priority=90
            ),
            
            # Advanced traffic management
            ConditionalPolicy(
                policy_name="KeyValueMapOperations",
                display_name="KVM-DynamicConfig",
                conditions=[
                    PolicyCondition("feature_flag", flag="advanced_traffic_management"),
                    PolicyCondition("system", systems=["payments", "orders"])
                ],
                params={"map_name": "dynamic-config", "scope": "environment"},
                priority=80
            )
        ])
    
    def get_applicable_policies(self, context: Dict[str, Any], 
                              flow_type: str = "preflow") -> List[Dict[str, Any]]:
        """
        Get list of policies that should be attached based on conditions.
        
        Args:
            context: Evaluation context (environment, features, API characteristics)
            flow_type: Type of flow (preflow, perflow, postflow)
            
        Returns:
            List of policy entries sorted by priority
        """
        applicable_policies = []
        
        for conditional_policy in self.conditional_policies:
            if conditional_policy.should_attach(context):
                policy_entry = conditional_policy.to_policy_entry()
                policy_entry["_priority"] = conditional_policy.priority
                applicable_policies.append(policy_entry)
                
                log.debug("conditional_policy_attached",
                         policy=conditional_policy.policy_name,
                         flow_type=flow_type,
                         conditions=len(conditional_policy.conditions))
        
        # Sort by priority (lower number = higher priority)
        applicable_policies.sort(key=lambda p: p.get("_priority", 100))
        
        # Remove priority field from final result
        for policy in applicable_policies:
            policy.pop("_priority", None)
        
        return applicable_policies
    
    def add_conditional_policy(self, conditional_policy: ConditionalPolicy):
        """Add a new conditional policy"""
        self.conditional_policies.append(conditional_policy)
    
    def evaluate_policy_conditions(self, context: Dict[str, Any]) -> Dict[str, bool]:
        """Evaluate all conditional policies and return their attachment status"""
        results = {}
        
        for conditional_policy in self.conditional_policies:
            results[conditional_policy.policy_name] = conditional_policy.should_attach(context)
        
        return results


def create_policy_condition(condition_config: Dict[str, Any]) -> PolicyCondition:
    """Create a PolicyCondition from configuration"""
    condition_type = condition_config.get("type")
    params = {k: v for k, v in condition_config.items() if k != "type"}
    return PolicyCondition(condition_type, **params)


def create_conditional_policy_from_config(config: Dict[str, Any]) -> ConditionalPolicy:
    """Create a ConditionalPolicy from configuration"""
    policy_name = config.get("policy")
    display_name = config.get("display_name")
    params = config.get("params", {})
    priority = config.get("priority", 100)
    
    conditions = []
    for condition_config in config.get("conditions", []):
        conditions.append(create_policy_condition(condition_config))
    
    return ConditionalPolicy(
        policy_name=policy_name,
        display_name=display_name,
        conditions=conditions,
        params=params,
        priority=priority
    )


# Global conditional policy attacher instance
_global_attacher: Optional[ConditionalPolicyAttacher] = None


def get_conditional_attacher() -> ConditionalPolicyAttacher:
    """Get or create the global conditional policy attacher"""
    global _global_attacher
    
    if _global_attacher is None:
        _global_attacher = ConditionalPolicyAttacher()
    
    return _global_attacher
