<!-- TargetEndpoint template -->
<TargetEndpoint name="{{ name }}">
  <HTTPTargetConnection>
    <URL>{{ url }}</URL>
    <SSLInfo>
      <Enabled>{{ 'true' if ssl.enabled else 'false' }}</Enabled>
    </SSLInfo>
    <Properties>
      <Property name="connect.timeout.millis">{{ timeouts.connect_ms }}</Property>
      <Property name="read.timeout.millis">{{ timeouts.read_ms }}</Property>
    </Properties>
  </HTTPTargetConnection>
</TargetEndpoint>
