"""
Exporter
--------
Exports a proxy revision as ZIP and (optionally) persists it to local storage.

Usage:
    exp = Exporter(apigee_client)
    result = exp.export(api, rev, bearer, save_local=True,
                        filename="bundles/<proxy>/rev-<rev>.zip")

Returns ExportResult with bytes, sha256, size, and optional local_path.
"""

from __future__ import annotations

from dataclasses import dataclass
from pathlib import Path
from typing import Optional

import structlog

from ..core.settings import settings
from .apigee_client import ApigeeClient
from ..adapters.hashing import sha256_bytes

log = structlog.get_logger(__name__)


@dataclass(frozen=True)
class ExportResult:
    data: bytes
    sha256: str
    size: int
    local_path: Optional[str] = None


class Exporter:
    def __init__(self, apigee: ApigeeClient) -> None:
        self._apigee = apigee

    def export(
        self,
        api: str,
        rev: int,
        bearer: str,
        *,
        save_local: bool = True,
        filename: Optional[str] = None,
    ) -> ExportResult:
        """
        Exports revision ZIP, computes sha256, and optionally saves locally.

        filename: relative path inside settings.storage_dir. If None, defaults to:
                  "bundles/{api}/rev-{rev}.zip"
        """
        blob = self._apigee.export_revision_zip(api, rev, bearer=bearer)
        sha = sha256_bytes(blob)
        local_path = None

        if save_local:
            rel = filename or f"bundles/{api}/rev-{rev}.zip"
            target = Path(settings.storage_dir).joinpath(rel).resolve()
            target.parent.mkdir(parents=True, exist_ok=True)
            target.write_bytes(blob)
            local_path = str(target)
            log.info("bundle_saved", path=local_path, size=len(blob))

        return ExportResult(data=blob, sha256=sha, size=len(blob), local_path=local_path)

