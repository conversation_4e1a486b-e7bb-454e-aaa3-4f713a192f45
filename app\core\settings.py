"""
Application settings (Pydantic v2 + pydantic-settings).

- Loads from .env (nested keys via MGMT__SSO_ZONE etc.)
- Uses default_factory for nested models to avoid import-time instantiation issues
- Provides safe defaults so the server can start even without .env
"""

from __future__ import annotations

from functools import lru_cache
from typing import Literal, Optional

from pydantic import AnyHttpUrl, Field, HttpUrl, SecretStr, field_validator, model_validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class RetryConfig(BaseSettings):
    total: int = Field(5, ge=0)
    backoff_factor: float = Field(0.5, ge=0.0)
    model_config = SettingsConfigDict(extra="ignore")


class MgmtConfig(BaseSettings):
    """
    Apigee Edge Management API + SSO token service config.
    NOTE: sso_zone has a default so the app can boot without .env.
    Override with MGMT__SSO_ZONE in your .env.
    """
    base_url: AnyHttpUrl = Field("https://arab-bank-plc-gw.login.apigee.com/v1")
    sso_zone: str = Field("aarab-bank-plc-gw", description="Edge SSO zone (e.g., 'arab-bank-plc-gw')")
    timeout_s: int = Field(30, ge=1)
    retries: RetryConfig = Field(default_factory=RetryConfig)

    apigee_token_url: str = Field("https://api-int.gcptest.arabbank.plc/ab/apigee-edge/bg/v1/token")
    x_channel_identifier: str = Field("MB")
    client_id: str = Field("dUJM50JfbAOOBdR1zU5H1mwgeXzcolKdfLTZHx5K37prqioj")
    basic_auth: str = Field("ZWRnZWNsaTplZGdlY2xpc2VjcmV0")

    # Optional confidential client for passcode exchange
    oauth_client_id: Optional[str] = None
    oauth_client_secret: Optional[SecretStr] = None

    model_config = SettingsConfigDict(extra="ignore")

    @property
    def token_url(self) -> str:
        return f"https://{self.sso_zone}.login.apigee.com/oauth/token"

    @model_validator(mode="after")
    def _validate_oauth_pair(self) -> "MgmtConfig":
        if (self.oauth_client_id and not self.oauth_client_secret) or (
            self.oauth_client_secret and not self.oauth_client_id
        ):
            raise ValueError(
                "Both mgmt.oauth_client_id and mgmt.oauth_client_secret must be provided together, or neither."
            )
        return self


class GitConfig(BaseSettings):
    base_url: str = Field("https://abrepo.arabbank.com.jo/api/v4")
    default_repo: HttpUrl | str = Field("https://gitlab.example.com/api-gateway/bundles.git")
    default_branch: str = Field("develop", min_length=1)
    pat: Optional[SecretStr] = None
    model_config = SettingsConfigDict(extra="ignore")


class Settings(BaseSettings):
    # App
    app_env: Literal["local", "dev", "test", "prod"] = "test"
    log_level: Literal["DEBUG", "INFO", "WARNING", "ERROR"] = "INFO"

    # Apigee org/env (creation context). Defaults so app can boot.
    apigee_org: str = Field("arabbank-test")
    apigee_env: str = Field("test")

    # Nested configs — use default_factory (critical)
    mgmt: MgmtConfig = Field(default_factory=MgmtConfig)
    git: GitConfig = Field(default_factory=GitConfig)

    # Limits / misc
    oas_max_mb: int = Field(5, ge=1)
    http_timeout_s: int = Field(30, ge=1)

    # Project-relative paths (you are using `app/...` layout)
    systems_dir: str = Field("app/config/systems")
    templates_dir: str = Field("app/templates")
    resources_dir: str = Field("app/resources")
    storage_dir: str = Field("app/storage")

    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
        # Enables MGMT__BASE_URL, GIT__DEFAULT_BRANCH, etc.
        env_nested_delimiter="__",
    )

    @field_validator("apigee_org", "apigee_env")
    @classmethod
    def _no_blanks(cls, v: str) -> str:
        v = (v or "").strip()
        if not v:
            raise ValueError("Value cannot be blank")
        return v


@lru_cache(maxsize=1)
def get_settings() -> Settings:
    return Settings()


# import as: from app.core.settings import settings
settings = get_settings()
