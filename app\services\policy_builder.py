"""
PolicyBuilder (env-aware, catalog-driven)
----------------------------------------
Renders Apigee policy XMLs from Jinja2 templates using 3 sources of param values:

  DEFAULT < ENV < INPUT

- DEFAULT: system YAML (systems/<system>.yaml)
- ENV:     per-env YAML (env/<env>/<system>.yaml), kept outside the system file
- INPUT:   API request payload + OAS-derived values (supports list ops)

Per-param merge semantics are defined by a small "param catalog" YAML, so lists
(e.g., CORS headers) can default to append_unique, while origins may replace, etc.

Templates should only reference {{ params.* }}; no default values in templates.
"""

from __future__ import annotations

from dataclasses import dataclass
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

import jinja2
import structlog
import yaml

from ..core.errors import ValidationError
from ..core.settings import settings
from .template_helpers import TemplateHelpers
from .feature_flags import get_feature_evaluator
from .conditional_policies import get_conditional_attacher

log = structlog.get_logger(__name__)


# ---------------------- Data models ----------------------

@dataclass(frozen=True)
class PolicyRender:
    name: str
    xml: str


@dataclass(frozen=True)
class AttachPlan:
    preflow_request: List[str]
    preflow_response: List[str]
    perflow_request: List[str]
    perflow_response: List[str]
    cors_policy: Optional[str] = None  # if configured/enabled


# ---------------------- Param catalog ----------------------

# Built-in param catalog defaults; can be overridden by YAML (see _load_param_catalog)
# type: string|int|bool|list ; merge for lists: replace|append_unique|prepend_unique|remove_only
# normalize: lower|exact (applies to list items)
_BUILTIN_PARAM_CATALOG: Dict[str, Dict[str, Dict[str, Any]]] = {
    "CORS": {
        "allow_origins":     {"type": "list", "merge": "replace",       "normalize": "exact"},
        "allow_methods":     {"type": "list", "merge": "append_unique", "normalize": "exact"},
        "allow_headers":     {"type": "list", "merge": "append_unique", "normalize": "lower"},
        "expose_headers":    {"type": "list", "merge": "append_unique", "normalize": "exact"},
        "max_age":           {"type": "int",  "merge": "replace"},
        "allow_credentials": {"type": "bool", "merge": "replace"},
    },
    "Quota": {
        "allow":          {"type": "string", "merge": "replace"},
        "interval":       {"type": "string", "merge": "replace"},
        "timeunit":       {"type": "string", "merge": "replace"},
        "identifier_ref": {"type": "string", "merge": "replace"},
    },
    "Spike-Arrest": {
        "rate": {"type": "string", "merge": "replace"},
    },
    "Verify-API-Key": {
        "keyref": {"type": "string", "merge": "replace"},
    },
    # Add others as needed (OAuthV2, ThreatProtection...)
}


def _load_param_catalog() -> Dict[str, Dict[str, Dict[str, Any]]]:
    """
    Load param catalog from consolidated policy catalog or fall back to built-in defaults.
    """
    try:
        from .policy_catalog import get_policy_catalog
        catalog = get_policy_catalog()
        param_catalog = catalog.get_param_catalog()

        # Merge with built-in defaults
        merged = {k: dict(v) for k, v in _BUILTIN_PARAM_CATALOG.items()}
        for pol, spec in param_catalog.items():
            merged.setdefault(pol, {})
            for k, cfg in (spec or {}).items():
                merged[pol][k] = cfg or {}

        log.info("param_catalog_loaded", source="policy_catalog.yaml", policies=len(param_catalog))
        return merged
    except Exception as e:
        log.warning("param_catalog_failed", error=str(e), using_builtin=True)
        return _BUILTIN_PARAM_CATALOG


def _load_template_mapping() -> Dict[str, str]:
    """
    Load policy template mapping from consolidated policy catalog.
    """
    try:
        from .policy_catalog import get_policy_catalog
        catalog = get_policy_catalog()
        mapping = catalog.get_template_mapping()

        if mapping:
            log.info("template_mapping_loaded", source="policy_catalog.yaml", policies=len(mapping))
            return mapping
    except Exception as e:
        log.warning("template_mapping_failed", error=str(e), using_fallback=True)

    # Fallback to basic mapping if catalog not available
    return {
        "VerifyApiKey": "VerifyApiKey.xml.j2",
        "SpikeArrest": "SpikeArrest.xml.j2",
        "Quota": "Quota.xml.j2",
        "CORS": "AssignMessage.CORS.xml.j2",
    }


# ---------------------- Policy builder ----------------------

class PolicyBuilder:
    def __init__(self, templates_dir: Optional[str] = None) -> None:
        tdir = templates_dir or settings.templates_dir
        base = Path(tdir).joinpath("policies").resolve()
        if not base.exists():
            raise ValidationError(
                f"Policies templates directory not found: {base}",
                details={"templates_dir": str(base)},
            )
        self._env = jinja2.Environment(
            loader=jinja2.FileSystemLoader(str(base)),
            autoescape=False,
            trim_blocks=True,
            lstrip_blocks=True,
        )
        self._env.undefined = jinja2.StrictUndefined
        self._templates_root = base

        self._param_catalog = _load_param_catalog()
        self._template_mapping = _load_template_mapping()

        # Initialize template helpers (will be updated per render call)
        self._template_helpers = None

        # Initialize policy catalog for validation
        try:
            from .policy_catalog import get_policy_catalog
            self._policy_catalog = get_policy_catalog()
        except Exception as e:
            log.warning("policy_catalog_init_failed", error=str(e))
            self._policy_catalog = None

    def render_policies(
        self,
        system_cfg: Dict[str, Any],
        input_vars: Dict[str, Any],
        *,
        system_name: Optional[str] = None,   # used to load env overrides if provided
    ) -> Tuple[List[PolicyRender], AttachPlan]:
        """
        Render all configured policies with DEFAULT < ENV < INPUT param resolution.
        - system_cfg: parsed systems/<system>.yaml
        - input_vars: UI + OAS dynamic context (may contain list ops)
        - system_name: optional; if given, try loading env overrides
        """
        # Initialize template helpers with current context
        env_name = getattr(settings, 'apigee_env', 'test')
        # Features are now system-driven, loaded from feature flags service
        try:
            from .feature_flags import get_feature_evaluator
            evaluator = get_feature_evaluator(env_name, system_name)
            # Create a basic features dict for template helpers
            system_features = {}
        except Exception:
            system_features = {}

        self._template_helpers = TemplateHelpers(env_name=env_name, features=system_features)

        # Initialize feature flag evaluator
        feature_evaluator = get_feature_evaluator(env_name, system_name)

        # Build context for conditional policy evaluation
        context = {
            "env_name": env_name,
            "system_name": system_name,
            "methods": input_vars.get("methods", []),
            "headers": input_vars.get("headers", []),
            "features": features,
            "security_policies": [],  # Will be populated as we process policies
            "content_types": self._extract_content_types(input_vars),
        }

        # Handle both new simplified structure and legacy structure
        system_cfg = system_cfg or {}

        # Check for new simplified structure first
        if "flows" in system_cfg:
            # New simplified structure: flows and policy_defaults at root level
            flows_cfg = system_cfg.get("flows", {})
            preflow_req = _normalize_entries(flows_cfg.get("preflow", {}).get("request", []))
            preflow_resp = _normalize_entries(flows_cfg.get("preflow", {}).get("response", []))
            perflow_req = _normalize_entries(flows_cfg.get("per_operation", {}).get("request", []))
            perflow_resp = _normalize_entries(flows_cfg.get("per_operation", {}).get("response", []))
            cors_cfg = flows_cfg.get("cors", {})
            sys_defaults = system_cfg.get("policy_defaults", {})
        else:
            # Legacy structure: policies.preflow, policies.defaults, etc.
            pol_cfg = system_cfg.get("policies", {})
            preflow_req = _normalize_entries(pol_cfg.get("preflow", {}).get("request", []))
            preflow_resp = _normalize_entries(pol_cfg.get("preflow", {}).get("response", []))
            perflow_req = _normalize_entries(pol_cfg.get("perflow", {}).get("request", []))
            perflow_resp = _normalize_entries(pol_cfg.get("perflow", {}).get("response", []))
            cors_cfg = pol_cfg.get("cors", {})
            sys_defaults = pol_cfg.get("defaults", {})

        # Combine system policies with input policies (from policy groups and direct input)
        # Also track feature-driven changes for recommendations
        combined_preflow_req, combined_preflow_resp, combined_perflow_req, combined_perflow_resp, combined_cors_cfg, feature_impact = self._combine_system_and_input_policies(
            preflow_req, preflow_resp, perflow_req, perflow_resp, cors_cfg, input_vars, system_name
        )

        # Store feature impact for response
        self._last_feature_impact = feature_impact

        # Optional env overrides (kept outside systems)
        env_overrides = _load_env_overrides(system_name) if system_name else {}

        # CORS configuration (use combined CORS config)
        cors_enabled = bool(combined_cors_cfg.get("enabled"))
        cors_name = None

        outputs: List[PolicyRender] = []
        seen_names: set[str] = set()

        def _render_entries(entries: List[Dict[str, Any]]) -> List[str]:
            names: List[str] = []
            for e in entries:
                # Handle both old and new configuration formats
                if "policy" in e:
                    # New simplified format: policy name + display name
                    policy_name = str(e.get("policy", "")).strip()
                    display_name = str(e.get("display_name", "")).strip()
                    attach_params = dict(e.get("params") or {})

                    if not policy_name:
                        raise ValidationError("Policy entry missing 'policy'", details={"entry": e})

                    # Look up template from mapping
                    template = self._template_mapping.get(policy_name)
                    if not template:
                        raise ValidationError(
                            f"No template mapping found for policy '{policy_name}'",
                            details={"policy": policy_name, "available": list(self._template_mapping.keys())}
                        )

                    # Generate display name automatically if not provided
                    if not display_name:
                        display_name = _generate_display_name(policy_name)

                    # Use display_name for the actual policy file name, policy_name for parameter resolution
                    pol_name = display_name
                    policy_label = policy_name  # Use policy name for parameter resolution

                else:
                    # Legacy format: name + template (backward compatibility)
                    pol_name = str(e.get("name", "")).strip()
                    template = str(e.get("template", "")).strip()
                    attach_params = dict(e.get("params") or {})
                    policy_label = pol_name  # Use name for parameter resolution

                    if not pol_name:
                        raise ValidationError("Policy entry missing 'name'", details={"entry": e})
                    if not template:
                        raise ValidationError("Policy entry missing 'template'", details={"name": pol_name})

                # Resolve params with DEFAULT < ENV < INPUT
                final_params = self._resolve_params(
                    policy_label=policy_label,
                    sys_defaults=sys_defaults,
                    env_overrides=env_overrides,
                    attach_params=attach_params,
                    input_vars=input_vars,
                )

                xml = self._render_template(template, name=pol_name, params=final_params, input=input_vars)

                if pol_name not in seen_names:
                    outputs.append(PolicyRender(name=pol_name, xml=xml))
                    seen_names.add(pol_name)
                else:
                    log.warning("duplicate_policy_name", name=pol_name)

                names.append(pol_name)
            return names

        # Use combined policies for rendering
        pre_req_names = _render_entries(combined_preflow_req)
        pre_resp_names = _render_entries(combined_preflow_resp)
        per_req_names = _render_entries(combined_perflow_req)
        per_resp_names = _render_entries(combined_perflow_resp)

        # Add conditional policies if feature is enabled
        if feature_evaluator.is_enabled("conditional_policies", context):
            conditional_attacher = get_conditional_attacher()

            # Get conditional policies for preflow
            conditional_preflow = conditional_attacher.get_applicable_policies(context, "preflow")
            if conditional_preflow:
                log.info("conditional_policies_attached",
                        flow="preflow",
                        count=len(conditional_preflow))
                conditional_names = _render_entries(conditional_preflow)
                pre_req_names.extend(conditional_names)

        # Optional: CORS as a dedicated policy entry with same merging
        if cors_enabled:
            # Handle both old and new CORS configuration formats (use combined config)
            if "policy" in combined_cors_cfg:
                # New simplified format
                policy_name = str(combined_cors_cfg.get("policy", "CORS")).strip()
                cors_name = str(combined_cors_cfg.get("display_name", "AM-AddCors")).strip()
                cors_template = self._template_mapping.get(policy_name, "AssignMessage.CORS.xml.j2")
                policy_label = policy_name
            else:
                # Legacy format
                cors_template = combined_cors_cfg.get("template", "AssignMessage.CORS.xml.j2")
                cors_name = str(combined_cors_cfg.get("name") or "CORS").strip()
                policy_label = "CORS"

            cors_params_attach = dict(combined_cors_cfg.get("params") or {})

            final_params = self._resolve_params(
                policy_label=policy_label,
                sys_defaults=sys_defaults,
                env_overrides=env_overrides,
                attach_params=cors_params_attach,
                input_vars=input_vars,
            )
            xml = self._render_template(cors_template, name=cors_name, params=final_params, input=input_vars)
            if cors_name not in seen_names:
                outputs.append(PolicyRender(name=cors_name, xml=xml))
                seen_names.add(cors_name)

        plan = AttachPlan(
            preflow_request=pre_req_names,
            preflow_response=pre_resp_names,
            perflow_request=per_req_names,
            perflow_response=per_resp_names,
            cors_policy=cors_name,
        )

        # Add feature impact to the response
        if hasattr(self, '_last_feature_impact'):
            return outputs, plan, self._last_feature_impact
        else:
            return outputs, plan, {}

    def validate_policy_params(self, policy_name: str, params: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        Validate parameters for a policy using the policy catalog.
        Returns (is_valid, list_of_errors)
        """
        if not self._policy_catalog:
            log.warning("policy_catalog_not_available", policy=policy_name)
            return True, []  # Skip validation if catalog not available

        return self._policy_catalog.validate_policy_params(policy_name, params)

    def validate_input_params(self, input_vars: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        Validate all policy parameters in input_vars.
        Returns (is_valid, list_of_errors)
        """
        if not self._policy_catalog:
            return True, []  # Skip validation if catalog not available

        policy_params = input_vars.get("policy_params", {})
        all_errors = []

        for policy_name, params in policy_params.items():
            is_valid, errors = self.validate_policy_params(policy_name, params)
            if not is_valid:
                all_errors.extend(errors)

        return len(all_errors) == 0, all_errors

    def _combine_system_and_input_policies(
        self,
        preflow_req: List[Dict[str, Any]],
        preflow_resp: List[Dict[str, Any]],
        perflow_req: List[Dict[str, Any]],
        perflow_resp: List[Dict[str, Any]],
        cors_cfg: Dict[str, Any],
        input_vars: Dict[str, Any],
        system_name: Optional[str] = None
    ) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]], List[Dict[str, Any]], List[Dict[str, Any]], Dict[str, Any], Dict[str, Any]]:
        """
        Combine system-defined policies with input policies (from policy groups and direct input).
        Creates a unified list of policies to render, avoiding duplicates.

        Priority order:
        1. System-defined policies (from flows configuration)
        2. Input policies from policy_params (including policy groups)
        3. System-driven feature flag evaluation for policy inclusion/exclusion

        Returns: (combined_preflow_req, combined_preflow_resp, combined_perflow_req, combined_perflow_resp, combined_cors_cfg, feature_impact)
        """
        # Get input policy parameters (includes policies from policy groups)
        policy_params = input_vars.get("policy_params", {})

        # Create sets to track which policies are already configured in system flows
        system_policies = set()
        for entries in [preflow_req, preflow_resp, perflow_req, perflow_resp]:
            for entry in entries:
                if isinstance(entry, dict) and "policy" in entry:
                    system_policies.add(entry["policy"])
                elif isinstance(entry, str):
                    system_policies.add(entry)

        # Add CORS policy if configured in system
        if cors_cfg.get("enabled") and cors_cfg.get("policy"):
            system_policies.add(cors_cfg["policy"])

        # Find input policies that are not already in system configuration
        additional_policies = []
        for policy_name, params in policy_params.items():
            if policy_name not in system_policies:
                # Create policy entry for additional policies
                policy_entry = {
                    "policy": policy_name,
                    "params": params if params else {}
                }
                additional_policies.append(policy_entry)

        # Apply system-driven feature flag filtering and track changes
        env_name = getattr(settings, 'apigee_env', 'test')
        filtered_additional_policies, feature_impact = self._apply_system_driven_feature_filtering(
            additional_policies, input_vars, env_name, system_name
        )

        # Combine policies - add additional policies to appropriate flows
        # For now, add additional policies to preflow request (can be made configurable)
        combined_preflow_req = list(preflow_req) + filtered_additional_policies
        combined_preflow_resp = list(preflow_resp)
        combined_perflow_req = list(perflow_req)
        combined_perflow_resp = list(perflow_resp)
        combined_cors_cfg = dict(cors_cfg)

        log.info(
            "policies_combined",
            system_policies=len(system_policies),
            additional_policies=len(filtered_additional_policies),
            total_preflow_req=len(combined_preflow_req),
            total_perflow_req=len(combined_perflow_req),
            feature_changes=len(feature_impact.get("recommendations", []))
        )

        return combined_preflow_req, combined_preflow_resp, combined_perflow_req, combined_perflow_resp, combined_cors_cfg, feature_impact

    def _apply_system_driven_feature_filtering(
        self,
        policies: List[Dict[str, Any]],
        input_vars: Dict[str, Any],
        env_name: str,
        system_name: Optional[str]
    ) -> Tuple[List[Dict[str, Any]], Dict[str, Any]]:
        """
        Apply system-driven feature flag filtering and track changes for recommendations.

        Args:
            policies: List of policy entries to filter
            input_vars: Input variables containing context
            env_name: Current environment name
            system_name: System name for feature flag evaluation

        Returns:
            Tuple of (filtered_policies, feature_impact_summary)
        """
        from .feature_flags import get_feature_evaluator

        # Initialize feature impact tracking
        feature_impact = {
            "policies_added": [],
            "policies_removed": [],
            "policies_modified": [],
            "recommendations": [],
            "active_feature_flags": {}
        }

        # Get feature flag evaluator for system-driven evaluation
        evaluator = get_feature_evaluator(env_name, system_name)

        filtered_policies = []

        for policy_entry in policies:
            policy_name = policy_entry.get("policy", "")

            # Check if policy should be included based on system-driven feature flags
            should_include, reason, feature_flag = self._should_include_policy_system_driven(
                policy_name, input_vars, evaluator
            )

            if should_include:
                filtered_policies.append(policy_entry)
                feature_impact["policies_added"].append(policy_name)

                if feature_flag:
                    feature_impact["active_feature_flags"][feature_flag] = True
                    feature_impact["recommendations"].append({
                        "policy_name": policy_name,
                        "action": "added",
                        "reason": reason,
                        "feature_flag": feature_flag,
                        "environment_impact": f"Policy added in {env_name} environment"
                    })
            else:
                feature_impact["policies_removed"].append(policy_name)

                if feature_flag:
                    feature_impact["active_feature_flags"][feature_flag] = False
                    feature_impact["recommendations"].append({
                        "policy_name": policy_name,
                        "action": "removed",
                        "reason": reason,
                        "feature_flag": feature_flag,
                        "environment_impact": f"Policy filtered out in {env_name} environment"
                    })

                log.debug("policy_filtered_by_system_feature_flag", policy=policy_name, reason=reason)

        return filtered_policies, feature_impact

    def _should_include_policy_system_driven(
        self,
        policy_name: str,
        input_vars: Dict[str, Any],
        evaluator
    ) -> Tuple[bool, str, Optional[str]]:
        """
        Determine if a policy should be included based on system-driven feature flags.

        Args:
            policy_name: Name of the policy to check
            input_vars: Full input context
            evaluator: Feature flag evaluator

        Returns:
            Tuple of (should_include, reason, feature_flag_name)
        """
        # Policy-specific feature flag mappings
        policy_feature_mappings = {
            "JSONThreatProtection": "json_threat_protection",
            "XMLThreatProtection": "xml_threat_protection",
            "ResponseCache": "smart_caching",
            "BasicAuthentication": "advanced_security",
            "MessageLogging": "debug_logging",
            "DebugPolicy": "debug_logging",
        }

        # Check if policy has a specific feature flag requirement
        feature_flag = policy_feature_mappings.get(policy_name)
        if feature_flag:
            # Create context for feature flag evaluation
            context = {
                "content_types": input_vars.get("content_types", []),
                "methods": input_vars.get("methods", []),
                "api_characteristics": {
                    "method_count": len(input_vars.get("methods", [])),
                    "has_security": any(policy in input_vars.get("policy_params", {})
                                      for policy in ["VerifyApiKey", "OAuthV2", "BasicAuthentication"])
                }
            }

            # Add content_type for single content type evaluation
            if context["content_types"]:
                context["content_type"] = context["content_types"][0]

            is_enabled = evaluator.is_enabled(feature_flag, context)
            reason = f"Feature flag '{feature_flag}' is {'enabled' if is_enabled else 'disabled'}"

            if not is_enabled:
                return False, reason, feature_flag

        # Content-type based filtering (additional check)
        content_types = input_vars.get("content_types", [])
        if policy_name == "JSONThreatProtection":
            json_content_types = ["application/json", "application/vnd.api+json"]
            has_json = any(ct in json_content_types for ct in content_types)
            if not has_json and content_types:  # Only filter if content types are specified
                return False, "No JSON content types detected", None
        elif policy_name == "XMLThreatProtection":
            xml_content_types = ["application/xml", "text/xml"]
            has_xml = any(ct in xml_content_types for ct in content_types)
            if not has_xml and content_types:  # Only filter if content types are specified
                return False, "No XML content types detected", None

        # Environment-based filtering for debug policies
        env_name = getattr(settings, 'apigee_env', 'test').lower()
        if policy_name in ["MessageLogging", "DebugPolicy"] and env_name in ['prod', 'production']:
            return False, "Debug policies disabled in production", "debug_logging"

        # Default: include the policy
        reason = "Policy included by default"
        return True, reason, feature_flag

    def _extract_content_types(self, input_vars: Dict[str, Any]) -> List[str]:
        """Extract content types from input variables (e.g., from OAS)"""
        content_types = []

        # Check if OAS context provides content types
        oas = input_vars.get("oas", {})
        if "request_content_types" in oas:
            content_types.extend(oas["request_content_types"])

        # Default content types based on common patterns
        methods = input_vars.get("methods", [])
        if any(method in ["POST", "PUT", "PATCH"] for method in methods):
            content_types.extend(["application/json", "application/xml"])

        return list(set(content_types))  # Remove duplicates

        log.info(
            "policies_rendered",
            pre_req=len(pre_req_names),
            pre_resp=len(pre_resp_names),
            per_req=len(per_req_names),
            per_resp=len(per_resp_names),
            cors=bool(cors_name),
        )
        return outputs, plan

    # -------------------- internals --------------------

    def _resolve_params(
        self,
        *,
        policy_label: str,
        sys_defaults: Dict[str, Any],
        env_overrides: Dict[str, Any],
        attach_params: Dict[str, Any],
        input_vars: Dict[str, Any],
    ) -> Dict[str, Any]:
        """
        Compute final params for a given policy using:
            DEFAULT < ENV < INPUT
        with per-param semantics from the param catalog + list ops.
        """
        # 0) Choose catalog key: use policy label if present, otherwise a generic mapping
        catalog = self._param_catalog.get(policy_label) or self._param_catalog.get(_canonical(policy_label)) or {}

        # 1) Collect layers (raw dicts) for the given policy
        def _get_legacy(d: Dict[str, Any], key: str) -> Dict[str, Any]:
            """Get params from legacy nested structure: policy.params"""
            return ((d.get(key) or {}).get("params")) if isinstance(d.get(key), dict) else {}

        def _get_simplified(d: Dict[str, Any], key: str) -> Dict[str, Any]:
            """Get params from simplified structure: direct policy parameters"""
            policy_data = d.get(key) or d.get(_canonical(key))
            if isinstance(policy_data, dict):
                return policy_data
            return {}

        # Try simplified structure first, then fall back to legacy
        layer_default = _get_simplified(sys_defaults, policy_label) or _get_legacy(sys_defaults, policy_label) or {}

        # Environment overrides - check both policy_defaults and legacy policies.overrides
        env_policy_defaults = env_overrides.get("policy_defaults", {})
        env_legacy_overrides = env_overrides.get("policies", {}).get("overrides", {})
        layer_env = _get_simplified(env_policy_defaults, policy_label) or _get_legacy(env_legacy_overrides, policy_label) or {}

        layer_attach = attach_params or {}

        # 2) Merge DEFAULT < ENV < ATTACH for non-list scalars & dicts;
        #    For lists, use param catalog semantics. Also collect YAML directives (__append etc.).
        merged = _merge_three_layers(catalog, layer_default, layer_env, layer_attach)

        # 3) Apply INPUT overlay (UI + OAS). INPUT can provide list ops or simple scalars.
        policy_input = _extract_policy_input(policy_label, input_vars)
        merged = _apply_input_overlay(catalog, merged, policy_input)

        # 4) Render any strings (e.g., "User sent {{ input.quota }}") using Jinja
        rendered = _render_params_dict(self._env, merged, {"input": input_vars, "env": settings.apigee_env})
        return rendered

    def _render_template(self, template_name: str, **context: Any) -> str:
        try:
            tmpl = self._env.get_template(template_name)
        except jinja2.TemplateNotFound as e:
            raise ValidationError(
                "Policy template not found",
                details={"template": template_name, "templates_root": str(self._templates_root)},
            ) from e

        # Add template helpers to context
        enhanced_context = dict(context)
        if self._template_helpers:
            enhanced_context['helpers'] = self._template_helpers
            # Also add helper functions directly for convenience
            enhanced_context.update({
                'is_valid_rate': self._template_helpers.is_valid_rate,
                'is_valid_url': self._template_helpers.is_valid_url,
                'safe_default': self._template_helpers.safe_default,
                'normalize_list': self._template_helpers.normalize_list,
                'is_production': self._template_helpers.is_production,
                'feature_enabled': self._template_helpers.feature_enabled,
                'xml_escape': self._template_helpers.xml_escape,
                'parse_rate_format': self._template_helpers.parse_rate_format,
            })

        try:
            xml: str = tmpl.render(**enhanced_context)
        except jinja2.UndefinedError as e:
            log.warning(
                "template_missing_variable",
                template=template_name,
                context=enhanced_context,
                error=str(e),
            )
            raise ValidationError(
                "Missing variable while rendering policy template",
                details={"template": template_name, "error": str(e)},
            ) from e

        return xml.strip()


# ---------------- helpers: loading & normalizing ----------------

def _canonical(s: str) -> str:
    return (s or "").strip()


def _generate_display_name(policy_name: str) -> str:
    """
    Generate display name following internal standard:
    - VerifyApiKey -> VK-VerifyApiKey
    - SpikeArrest -> SA-SpikeArrest
    - Quota -> Q-ImposeQuota
    - CORS -> AM-AddCors
    - ExtractVariables -> EV-ExtractVariables
    - etc.
    """
    # Special cases for common policies
    special_cases = {
        "VerifyApiKey": "VK-VerifyApiKey",
        "SpikeArrest": "SA-SpikeArrest",
        "Quota": "Q-ImposeQuota",
        "CORS": "AM-AddCors",
        "AssignMessage": "AM-AssignMessage",
        "ExtractVariables": "EV-ExtractVariables",
        "RaiseFault": "RF-RaiseFault",
        "FlowCallout": "FC-FlowCallout",
        "ResponseCache": "RC-ResponseCache",
        "PopulateCache": "PC-PopulateCache",
        "LookupCache": "LC-LookupCache",
        "InvalidateCache": "IC-InvalidateCache",
        "KeyValueMapOperations": "KVM-KeyValueMapOperations",
        "JSONThreatProtection": "JTP-JSONThreatProtection",
        "XMLThreatProtection": "XTP-XMLThreatProtection",
        "RegularExpressionProtection": "REP-RegularExpressionProtection",
        "DecodeJWT": "DJ-DecodeJWT",
        "VerifyJWT": "VJ-VerifyJWT",
        "GenerateJWT": "GJ-GenerateJWT",
        "OAuthV2": "OA-OAuthV2",
        "BasicAuthentication": "BA-BasicAuthentication",
        "AccessControl": "AC-AccessControl",
        "JSONToXML": "J2X-JSONToXML",
        "XMLToJSON": "X2J-XMLToJSON",
        "XSL": "XSL-Transform",
    }

    if policy_name in special_cases:
        return special_cases[policy_name]

    # Default: use first two letters as prefix
    prefix = policy_name[:2].upper() if len(policy_name) >= 2 else policy_name.upper()
    return f"{prefix}-{policy_name}"

def _normalize_entries(value: Any) -> List[Dict[str, Any]]:
    """
    Normalize policy entries to support both old and new formats:
    - Old format: [{"policy": "VerifyApiKey", "display_name": "VK-VerifyAPIKey"}, ...]
    - New format: ["VerifyApiKey", "SpikeArrest", ...]
    """
    if not value:
        return []
    if not isinstance(value, list):
        raise ValidationError("Policy list must be a list", details={"value": value})

    normalized = []
    for e in value:
        if isinstance(e, str):
            # New simplified format: just policy name as string
            normalized.append({"policy": e.strip()})
        elif isinstance(e, dict):
            # Old format: already a dict, keep as-is
            normalized.append(e)
        else:
            raise ValidationError("Policy entry must be a string or object", details={"entry": e})

    return normalized

def _load_env_overrides(system_name: Optional[str]) -> Dict[str, Any]:
    """
    Load env overrides YAML from <envs_dir>/<env>/<system>.yaml if present.
    envs_dir defaults to sibling of systems_dir: config/env
    """
    if not system_name:
        return {}
    sys_dir = Path(settings.systems_dir).resolve()
    envs_dir = getattr(settings, "envs_dir", None)
    env_root = Path(envs_dir) if envs_dir else sys_dir.parent.joinpath("env")
    path = env_root.joinpath(settings.apigee_env, f"{system_name}.yaml")
    if not path.exists():
        log.debug("env_overrides_not_found", path=str(path))
        return {}
    try:
        with open(path, "r", encoding="utf-8") as f:
            y = yaml.safe_load(f) or {}
        log.info("env_overrides_loaded", system=system_name, env=settings.apigee_env, path=str(path))
        return y
    except Exception as e:
        log.warning("env_overrides_failed", path=str(path), error=str(e))
        return {}

# ---------------- helpers: merging ----------------

def _merge_three_layers(
    catalog: Dict[str, Dict[str, Any]],
    default_layer: Dict[str, Any],
    env_layer: Dict[str, Any],
    attach_layer: Dict[str, Any],
) -> Dict[str, Any]:
    """
    Merge DEFAULT < ENV < ATTACH for params dicts using per-key semantics from catalog.
    - Scalars: replace
    - Dicts: shallow merge (later overrides earlier)
    - Lists: follow catalog.merge (replace|append_unique|prepend_unique). Also resolve YAML directives.
    """
    # Start with empty; fold in three layers
    result: Dict[str, Any] = {}

    # We also collect directives like key__append from all layers and apply at the end.
    directives: Dict[str, Dict[str, List[Any]]] = {}

    def fold(src: Dict[str, Any]):
        for key, val in (src or {}).items():
            if "__" in key and key.split("__", 1)[1] in ("append", "prepend", "replace", "remove"):
                k, op = key.split("__", 1)
                directives.setdefault(k, {}).setdefault(op, [])
                directives[k][op].extend(_as_list(val))
                continue

            spec = catalog.get(key, {})
            typ = (spec.get("type") or _infer_type(val))
            merge_mode = spec.get("merge", "replace")

            if typ == "list":
                base = _as_list(result.get(key))
                incoming = _as_list(val)
                if base is None:
                    base = []
                if incoming is None:
                    incoming = []

                if merge_mode == "replace":
                    result[key] = list(incoming)
                elif merge_mode == "append_unique":
                    result[key] = _append_unique(base, incoming)
                elif merge_mode == "prepend_unique":
                    result[key] = _prepend_unique(base, incoming)
                else:  # fallback to replace
                    result[key] = list(incoming)
            elif typ == "dict" and isinstance(val, dict):
                dest = result.get(key, {})
                if not isinstance(dest, dict):
                    dest = {}
                tmp = dict(dest)
                tmp.update(val)
                result[key] = tmp
            else:
                result[key] = val

    # Apply layers
    for layer in (default_layer, env_layer, attach_layer):
        fold(layer)

    # Apply directives after base merges
    for k, ops in directives.items():
        spec = catalog.get(k, {})
        typ = spec.get("type", "list")
        norm = spec.get("normalize")
        current = _as_list(result.get(k)) or []
        if "replace" in ops:
            current = _as_list(ops["replace"])
        if "prepend" in ops:
            current = _prepend_unique(current, _as_list(ops["prepend"]))
        if "append" in ops:
            current = _append_unique(current, _as_list(ops["append"]))
        if "remove" in ops:
            current = [x for x in current if x not in set(_as_list(ops["remove"]))]
        # normalize & dedupe
        if typ == "list":
            current = _normalize_list(current, norm)
        result[k] = current

    # Normalize lists from non-directive merges too
    for k, v in list(result.items()):
        spec = catalog.get(k, {})
        if spec.get("type") == "list":
            result[k] = _normalize_list(_as_list(v) or [], spec.get("normalize"))

    return result


def _apply_input_overlay(
    catalog: Dict[str, Dict[str, Any]],
    merged: Dict[str, Any],
    policy_input: Dict[str, Any],
) -> Dict[str, Any]:
    """
    Apply INPUT layer to an already-merged dict, respecting catalog semantics and list ops.
    INPUT can provide:
      - Scalars (replace)
      - Lists (treated as 'replace' unless catalog says append_unique)
      - Dict list-ops: {append:[], prepend:[], replace:[], remove:[]}
    """
    out = dict(merged)

    for key, val in (policy_input or {}).items():
        spec = catalog.get(key, {})
        typ = spec.get("type", _infer_type(val))
        norm = spec.get("normalize")
        default_list_mode = spec.get("merge", "replace")

        if typ == "list":
            base = _as_list(out.get(key)) or []
            # Dict with ops?
            if isinstance(val, dict):
                current = list(base)
                if "replace" in val:
                    current = _as_list(val.get("replace"))
                if "prepend" in val:
                    current = _prepend_unique(current, _as_list(val.get("prepend")))
                if "append" in val:
                    current = _append_unique(current, _as_list(val.get("append")))
                if "remove" in val:
                    current = [x for x in current if x not in set(_as_list(val.get("remove")))]
                out[key] = _normalize_list(current, norm)
            else:
                # plain list input: obey default strategy
                incoming = _as_list(val) or []
                if default_list_mode == "append_unique":
                    out[key] = _normalize_list(_append_unique(base, incoming), norm)
                elif default_list_mode == "prepend_unique":
                    out[key] = _normalize_list(_prepend_unique(base, incoming), norm)
                else:  # replace
                    out[key] = _normalize_list(incoming, norm)
        elif typ == "dict" and isinstance(val, dict):
            tmp = dict(out.get(key) or {})
            tmp.update(val)
            out[key] = tmp
        else:
            out[key] = val

    return out


# ---------------- helpers: rendering params ----------------

def _render_params_dict(env: jinja2.Environment, params: Dict[str, Any], ctx: Dict[str, Any]) -> Dict[str, Any]:
    def _render(val: Any) -> Any:
        if isinstance(val, str):
            t = env.from_string(val)
            return t.render(**ctx)
        elif isinstance(val, dict):
            return {k: _render(v) for k, v in val.items()}
        elif isinstance(val, list):
            return [_render(v) for v in val]
        return val
    return _render(params)


# ---------------- helpers: types & lists ----------------

def _infer_type(val: Any) -> str:
    if isinstance(val, list):
        return "list"
    if isinstance(val, bool):
        return "bool"
    if isinstance(val, int):
        return "int"
    if isinstance(val, dict):
        return "dict"
    return "string"

def _as_list(val: Any) -> Optional[List[Any]]:
    if val is None:
        return None
    if isinstance(val, list):
        return list(val)
    return [val]

def _append_unique(base: List[Any], incoming: List[Any]) -> List[Any]:
    seen = set(base)
    out = list(base)
    for x in incoming or []:
        if x not in seen:
            out.append(x)
            seen.add(x)
    return out

def _prepend_unique(base: List[Any], incoming: List[Any]) -> List[Any]:
    seen = set(base)
    out: List[Any] = []
    for x in incoming or []:
        if x not in seen:
            out.append(x)
            seen.add(x)
    out.extend(base)
    return out

def _normalize_list(items: List[Any], mode: Optional[str]) -> List[Any]:
    if not mode:
        # ensure uniqueness while preserving order
        return _append_unique([], items)
    if mode == "lower":
        normalized = [str(x).lower() for x in items]
        return _append_unique([], normalized)
    # 'exact' or unknown → unique preserve order
    return _append_unique([], items)


# ---------------- helpers: input extraction ----------------

def _extract_policy_input(policy_label: str, input_vars: Dict[str, Any]) -> Dict[str, Any]:
    """
    Enhanced policy input extraction with direct policy name mapping support:
      - Direct policy parameters: input.policy_params.<PolicyName> (direct parameter mapping)
      - Legacy structured overrides: input.policy.<PolicyName> (structured list ops)
      - Legacy convenience mappings:
          Quota.allow          <- input.quota
          Spike-Arrest.rate    <- input.spikeArrest
          CORS.allow_methods   <- input.methods (+ maybe oas.methods if present)
          CORS.allow_headers   <- input.headers (+ oas.request_headers)
          CORS.expose_headers  <- oas.response_headers

    Precedence: Direct Policy Params > Legacy Structured > Legacy Convenience
    """
    out: Dict[str, Any] = {}

    # 1. Enhanced direct policy parameter mapping (highest precedence)
    policy_params = input_vars.get("policy_params", {}) if isinstance(input_vars, dict) else {}
    if isinstance(policy_params, dict):
        # Look for exact policy name match first
        if policy_label in policy_params and isinstance(policy_params[policy_label], dict):
            out.update(policy_params[policy_label])

        # Also check for canonical policy name mapping (e.g., "Spike-Arrest" -> "SpikeArrest")
        canonical_label = _canonical(policy_label)
        if canonical_label != policy_label and canonical_label in policy_params:
            if isinstance(policy_params[canonical_label], dict):
                # Merge with lower precedence than exact match
                for k, v in policy_params[canonical_label].items():
                    if k not in out:  # Don't override exact matches
                        out[k] = v

    # 2. Legacy structured per-policy map from input: { policy: { key: {append/prepend/replace/remove} } }
    policy_map = (input_vars.get("policy") or {}) if isinstance(input_vars, dict) else {}
    if isinstance(policy_map.get(policy_label), dict):
        # Merge with lower precedence than direct params
        for k, v in policy_map[policy_label].items():
            if k not in out:  # Don't override direct params
                out[k] = v

    # 3. OAS-derived and method/header convenience overlays (lowest precedence)

    if policy_label == "CORS":
        # caller may set methods/headers; treat as append_unique by default
        if "methods" in input_vars and "allow_methods" not in out:
            out.setdefault("allow_methods", {})  # dict ops
            out["allow_methods"].setdefault("append", [])
            out["allow_methods"]["append"] += _as_list(input_vars["methods"]) or []

        if "headers" in input_vars and "allow_headers" not in out:
            out.setdefault("allow_headers", {})
            out["allow_headers"].setdefault("append", [])
            out["allow_headers"]["append"] += _as_list(input_vars["headers"]) or []

        # OAS-derived headers (only if not already set)
        oas = input_vars.get("oas") or {}
        if "request_headers" in oas and "allow_headers" not in out:
            out.setdefault("allow_headers", {})
            out["allow_headers"].setdefault("append", [])
            out["allow_headers"]["append"] += _as_list(oas["request_headers"]) or []
        if "response_headers" in oas and "expose_headers" not in out:
            out.setdefault("expose_headers", {})
            out["expose_headers"].setdefault("append", [])
            out["expose_headers"]["append"] += _as_list(oas["response_headers"]) or []

    return out