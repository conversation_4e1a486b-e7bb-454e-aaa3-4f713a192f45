from fastapi import APIRouter
from .endpoints import health, ready, bootstrap, policies

router = APIRouter()
router.include_router(health.router, prefix="/health", tags=["health"])
router.include_router(ready.router, prefix="/ready", tags=["readiness"])
router.include_router(bootstrap.router, prefix="/bootstrap", tags=["bootstrap"])
router.include_router(policies.router, prefix="/policies", tags=["policies"])
