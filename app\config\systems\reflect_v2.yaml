# Simplified System Configuration for Apigee Proxy Framework
# Version 2.0 - Streamlined and intuitive structure

# Basic system metadata
metadata:
  system: "payments"
  owner_team: "payments-platform"
  description: "Payments domain APIs"
  version: "2.0"

# Git repository configuration
git:
  bundles_repo: "api-and-gateway/api-deployment/digital-apps/reflect/edge/proxy"
  source_repo: "api-and-gateway/api-deployment/digital-apps/reflect/edge/proxy"
  default_branch: "develop"

# Security configuration
security:
  type: "apikey"  # apikey | oauth2 | jwt
  policies: ["VerifyApiKey"]  # Primary security policies

# Policy defaults - simplified structure
policy_defaults:
  # Security Policies
  VerifyApiKey:
    keyref: "request.header.client_id"
  
  OAuthV2:
    operation: "VerifyAccessToken"
    access_token_ref: "request.header.Authorization"
    remove_prefix: true
  
  # Traffic Management
  SpikeArrest:
    rate: "100pm"
  
  Quota:
    allow: "100"
    interval: "1"
    timeunit: "minute"
    identifier_ref: "request.header.client_id"
  
  # CORS Configuration
  CORS:
    allow_origins: ["*"]
    allow_methods: ["OPTIONS"]
    allow_headers: ["accept", "content-type", "client_id", "message-id"]
    expose_headers: ["x-request-id"]
    max_age: 86400
    allow_credentials: false
  
  # Data Extraction
  ExtractVariables:
    source: "request"
    ignore_unresolved: true
  
  # Caching
  ResponseCache:
    cache_resource: "response-cache"
    scope: "environment"
    expiry_seconds: 60
  
  # Threat Protection
  JSONThreatProtection:
    source: "request"
    max_container_depth: 32
    max_array_elements: 10000
    max_object_members: 1000
    max_string_length: 8192
  
  XMLThreatProtection:
    source: "request"
    max_depth: 32
    max_attributes: 128
    max_children: 128
    deny_doctype: true

# Flow attachment configuration - simplified
flows:
  # Policies that run before all API flows
  preflow:
    request:
      - VerifyApiKey
      - SpikeArrest
    response: []
  
  # Policies that run for each API operation
  per_operation:
    request:
      - Quota
    response: []
  
  # CORS handling
  cors:
    enabled: true
    policy: CORS

# Target endpoint configuration
target:
  name: "default"
  baseurl: "https://payments.internal.example.com"
  connect_timeout_ms: 4000
  read_timeout_ms: 30000
  ssl_profile: "SSLInfo"

# Flow generation settings
flow_settings:
  include_verb_in_condition: true
  regex_required: false

# Resource files to include
resources:
  - path: "xsl/transform/normalize.xsl"
    type: "resource"

# Feature flags
features:
  advanced_security: false
  caching_enabled: true
  threat_protection: false
