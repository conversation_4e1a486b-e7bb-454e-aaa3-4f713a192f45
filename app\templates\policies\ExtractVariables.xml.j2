<ExtractVariables name="{{ name }}">
  {% if params.source %}<Source>{{ params.source }}</Source>{% endif %} <!-- request | response | variable name -->
  {% if params.json %}
  <JSONPayload>
    {% for var in params.json %}
    <Variable name="{{ var.name }}">
      <JSONPath>{{ var.path }}</JSONPath>
    </Variable>
    {% endfor %}
  </JSONPayload>
  {% endif %}
  {% if params.xml %}
  <XMLPayload>
    {% for var in params.xml %}
    <Variable name="{{ var.name }}">
      <XPath>{{ var.xpath }}</XPath>
    </Variable>
    {% endfor %}
  </XMLPayload>
  {% endif %}
  {% if params.form_params %}
  <FormParams>
    {% for fp in params.form_params %}
    <FormParam name="{{ fp.name }}">{{ fp.variable }}</FormParam>
    {% endfor %}
  </FormParams>
  {% endif %}
  {% if params.headers %}
  <Headers>
    {% for h in params.headers %}
    <Header name="{{ h.name }}">{{ h.variable }}</Header>
    {% endfor %}
  </Headers>
  {% endif %}
  {% if params.regex %}
  <Regex>
    <Pattern>{{ params.regex.pattern }}</Pattern>
    {% for v in params.regex.variables %}
    <Variable name="{{ v.name }}">{{ v.group }}</Variable>
    {% endfor %}
  </Regex>
  {% endif %}
  {% if params.ignore_unresolved is defined %}<IgnoreUnresolvedVariables>{{ 'true' if params.ignore_unresolved else 'false' }}</IgnoreUnresolvedVariables>{% endif %}
</ExtractVariables>
