# app/api/endpoints/health.py
from fastapi import APIRouter
from pydantic import BaseModel
from datetime import datetime

router = APIRouter()

class HealthResponse(BaseModel):
    status: str
    timestamp: datetime
    service: str

@router.get("/", response_model=HealthResponse, summary="Health Check")
async def health_check():
    """
    Basic health check endpoint to verify that the service is running.
    Returns status, timestamp, and service identifier.
    """
    return HealthResponse(
        status="ok",
        timestamp=datetime.utcnow(),
        service="apigee-bootstrap-service"
    )
