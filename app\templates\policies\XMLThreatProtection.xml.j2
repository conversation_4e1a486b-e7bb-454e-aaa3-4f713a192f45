<XMLThreatProtection name="{{ name }}">
  {% if params.source %}<Source>{{ params.source }}</Source>{% endif %} <!-- variable or request/response -->
  {% if params.max_depth is defined %}<MaxDepth>{{ params.max_depth }}</MaxDepth>{% endif %}
  {% if params.max_attributes is defined %}<MaxAttributes>{{ params.max_attributes }}</MaxAttributes>{% endif %}
  {% if params.max_attribute_length is defined %}<MaxAttributeValueLength>{{ params.max_attribute_length }}</MaxAttributeValueLength>{% endif %}
  {% if params.max_children is defined %}<MaxChildren>{{ params.max_children }}</MaxChildren>{% endif %}
  {% if params.deny_doctype is defined %}<DisallowDoctypeDecl>{{ 'true' if params.deny_doctype else 'false' }}</DisallowDoctypeDecl>{% endif %}
  {% if params.external_entities is defined %}<ExternalGeneralEntities>{{ 'true' if params.external_entities else 'false' }}</ExternalGeneralEntities>{% endif %}
  {% if params.comments is defined %}<Comments>{{ 'true' if params.comments else 'false' }}</Comments>{% endif %}
</XMLThreatProtection>