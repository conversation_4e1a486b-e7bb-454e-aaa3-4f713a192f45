from __future__ import annotations

import json
from typing import Any, Dict, Optional

from fastapi import APIRouter, File, Form, HTTPException, UploadFile

from ....core.errors import ValidationError
from ....services.orchestrator import (
    BootstrapRequest as OrchestratorBootstrapRequest,
    GitOptions as OrchestratorGitOptions,
    Orchestrator,
)
from ..models.bootstrap import BootstrapResponse

router = APIRouter()


@router.post(
    "/",
    response_model=BootstrapResponse,
    summary="Bootstrap a new Apigee proxy (rev 1) from Swagger/OAS with enhanced policy parameter support",
)
async def bootstrap(
    # --- required file ---
    swagger_file: UploadFile = File(..., description="Swagger/OpenAPI file (JSON or YAML)"),
    # --- required metadata ---
    passcode: str = Form(..., description="Apigee Edge SSO passcode"),
    systemName: str = Form(..., description="System key (matches systems/<name>.yaml)"),
    proxyName: str = Form(..., description="Apigee proxy name to create"),

    # --- policy configuration options ---
    policyParams: Optional[str] = Form(
        None,
        description='JSON string of policy parameters using direct policy name mapping. '
                   'Example: {"VerifyApiKey": {"keyref": "request.header.x-custom-key"}, '
                   '"CORS": {"allow_origins": ["https://app.example.com"]}}'
    ),

    policyGroup: Optional[str] = Form(
        None,
        description='Policy group/template name to apply (e.g., "basic_security", "cors_enabled", "high_security"). '
                   'When specified, applies all policies in the group. Can be combined with policyParams for customization.'
    ),

    # --- flow configuration ---
    flowConfig: Optional[str] = Form(
        None,
        description='JSON string of custom flow configuration'
    ),

    # --- environment overrides ---
    envOverrides: Optional[str] = Form(
        None,
        description='JSON string of environment-specific parameter overrides'
    ),



    # --- optional Git commit message ---
    gitCommitMessage: Optional[str] = Form(None, description="Commit message (repo URL and branch read from system config)"),
):
    """
    Bootstrap a new Apigee proxy from Swagger/OAS with feature-driven policy selection.

    This endpoint creates a new proxy (revision 1) for LOWER ENVIRONMENTS ONLY (dev/test) with:
    - Automatic policy attachment based on system configuration and policy groups
    - Feature-driven policy selection based on system-defined feature flags
    - Enhanced parameter resolution (system defaults < env overrides < input params)
    - Policy recommendations showing feature-based changes
    - Direct policy name mapping for intuitive parameter specification
    - Optional Git integration for automated deployment

    Note: For production deployments, use the separate production deployment pipeline.
    """

    # Validate environment - bootstrap API only for lower environments
    from ....core.settings import settings
    current_env = getattr(settings, 'apigee_env', 'test').lower()
    if current_env in ['prod', 'production']:
        raise HTTPException(
            status_code=403,
            detail="Bootstrap API is not available for production environment. Use production deployment pipeline instead."
        )

    try:
        # Read file
        swagger_bytes = await swagger_file.read()
        if not swagger_bytes:
            raise ValidationError("Uploaded swagger_file is empty")

        # Parse dynamic parameters
        parsed_policy_params = {}
        parsed_flow_config = {}
        parsed_env_overrides = {}

        try:
            if policyParams:
                parsed_policy_params = json.loads(policyParams)
                if not isinstance(parsed_policy_params, dict):
                    raise ValidationError("policyParams must be a JSON object")

            if flowConfig:
                parsed_flow_config = json.loads(flowConfig)
                if not isinstance(parsed_flow_config, dict):
                    raise ValidationError("flowConfig must be a JSON object")

            if envOverrides:
                parsed_env_overrides = json.loads(envOverrides)
                if not isinstance(parsed_env_overrides, dict):
                    raise ValidationError("envOverrides must be a JSON object")



        except json.JSONDecodeError as e:
            raise ValidationError(f"Invalid JSON in parameter: {str(e)}")

        # Handle policy group expansion and combination with system policies
        combined_policy_params = parsed_policy_params.copy()

        if policyGroup:
            try:
                from ....services.policy_catalog import get_policy_catalog
                catalog = get_policy_catalog()

                if not catalog.validate_group_exists(policyGroup):
                    raise ValidationError(f"Policy group '{policyGroup}' does not exist")

                # Get policies in the group
                group_policies = catalog.get_group_policies(policyGroup)

                # Add group policies to combined policy params if not already specified
                for policy_name in group_policies:
                    if policy_name not in combined_policy_params:
                        # Add policy with empty params (will use system/env defaults)
                        combined_policy_params[policy_name] = {}

            except Exception as e:
                raise ValidationError(f"Failed to process policy group '{policyGroup}': {str(e)}")

        # Store the combined policy params for the orchestrator
        parsed_policy_params = combined_policy_params

        # Git options - only commit message from API, repo URL and branch from system config
        git_opts = None
        if gitCommitMessage:
            git_opts = OrchestratorGitOptions(
                commit_message=gitCommitMessage,
                # repo_url and branch will be read from system config
            )

        orch_req = OrchestratorBootstrapRequest(
            passcode=passcode,
            system_name=systemName,
            proxy_name=proxyName,
            swagger_content=swagger_bytes,
            git=git_opts,
            # Enhanced parameters
            policy_params=parsed_policy_params,
            flow_config=parsed_flow_config,
            env_overrides=parsed_env_overrides,
        )

        # Execute orchestration
        orch = Orchestrator()
        result = orch.bootstrap(orch_req)

        # Create feature impact summary for response
        feature_impact_summary = None
        if result.feature_impact:
            feature_impact_summary = FeatureImpactSummary(
                policies_added=result.feature_impact.get("policies_added", []),
                policies_removed=result.feature_impact.get("policies_removed", []),
                policies_modified=result.feature_impact.get("policies_modified", []),
                recommendations=[
                    PolicyRecommendation(**rec) for rec in result.feature_impact.get("recommendations", [])
                ],
                active_feature_flags=result.feature_impact.get("active_feature_flags", {})
            )

        return BootstrapResponse(
            proxyName=result.proxy_name,
            revision=result.revision,
            apigeeOrg=result.org,
            apigeeEnv=result.env,
            bundleSha256=result.bundle_sha256,
            exportSize=result.export_size,
            gitCommitId=result.git_commit_id,
            gitWebUrl=result.git_web_url,
            feature_impact=feature_impact_summary,
            policies_generated=result.policies_generated or [],
            environment_notes=result.environment_notes,
        )

    except ValidationError as ve:
        # Domain-style error; let global handler format it
        raise ve
    except HTTPException:
        raise
    except Exception as e:
        # Bubble to global unhandled exception handler
        raise e