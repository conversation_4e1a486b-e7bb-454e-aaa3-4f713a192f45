"""
GitService (GitLab)
-------------------
Commits the exported ZIP into a GitLab repository using the GitLab REST API
(Create Commit with actions). No git binaries required.

- Derives API base as: https://<gitlab-host>/api/v4
- Project ID is the URL-encoded repo path (strip .git), e.g. "group/subgroup/repo"

Default file path logic:
- Attempts to parse `commit_message` like "feat: bootstrap <proxyName>@rev<rev>"
  and uses "bundles/<proxyName>/rev-<rev>.zip".
- If parsing fails, falls back to "bundles/export-<sha256>.zip".

Usage:
    gs = GitService()
    commit_id, web_url = gs.push_zip(zip_bytes, repo_url, branch, message, file_path=None)
"""

from __future__ import annotations

import base64
import re
from dataclasses import dataclass
from typing import Optional, Tuple
from urllib.parse import urlparse, quote_plus

import zipfile
import io

import httpx
import structlog

from ..core.errors import UpstreamError, ValidationError
from ..core.settings import settings
from ..adapters.hashing import sha256_bytes

log = structlog.get_logger(__name__)


@dataclass(frozen=True)
class GitPushResult:
    commit_id: str
    web_url: Optional[str]


class GitService:
    def __init__(
        self,
        *,
        http: Optional[httpx.Client] = None,
        timeout_s: Optional[int] = None,
    ) -> None:
        self._base_url = settings.git.base_url
        self._pat = settings.git.pat.get_secret_value() if settings.git.pat else None
        if not self._pat:
            log.warning("git_pat_missing", msg="Proceeding without PAT may fail for private repos.")
        self._http = http or httpx.Client(timeout=timeout_s or 30)

    def push_zip_source_code(
            self,
            zip_bytes: bytes,
            project_id: str,
            branch: str,
            commit_message: str,
            *,
            file_path: Optional[str] = None,
        ) -> Tuple[str, Optional[str]]:
        """
        Creates a commit that adds/updates:
        1) The actual content of the ZIP file (files and folders) directly into the repository.
        2) The ZIP file itself inside the /bundles folder.

        Returns (commit_id, web_url).
        """
        if not project_id or not branch or not commit_message:
            raise ValidationError("project_id, branch, and commit_message are required")

        base_url = self._base_url
        headers = _auth_headers(self._pat)

        # Resolve path for ZIP file
        if not file_path:
            guessed = _guess_path_from_message(commit_message)
            file_path = guessed or f"bundles/export-{sha256_bytes(zip_bytes)[:12]}.zip"

        # Prepare list of actions for the commit
        actions = []

        # 1. Add the ZIP file to the /bundles folder
        encoded_zip = base64.b64encode(zip_bytes).decode("ascii")
        actions.append({
            "action": "create",
            "file_path": file_path,
            "content": encoded_zip,
            "encoding": "base64",
        })

        # 2. Extract the ZIP file and add its contents to the repository
        try:
            with zipfile.ZipFile(io.BytesIO(zip_bytes), "r") as zip_ref:
                for file_name in zip_ref.namelist():
                    # Skip directories (GitLab API handles files only)
                    if file_name.endswith("/"):
                        continue

                    # Read file content from the ZIP
                    file_content = zip_ref.read(file_name).decode("utf-8", errors="ignore")

                    # Add action for each file
                    actions.append({
                        "action": "update",
                        "file_path": file_name,  # File path relative to the repository root
                        "content": file_content,
                    })
        except zipfile.BadZipFile:
            raise ValidationError("Invalid ZIP file provided")

        # Build commit body
        body = {
            "branch": branch,
            "commit_message": commit_message,
            "actions": actions,
        }

        # Create commit via GitLab API
        url = f"{base_url}/projects/{project_id}/repository/commits"
        resp = self._http.post(url, headers=headers, json=body)

        # Retry as update if any file already exists
        if resp.status_code == 400 and "A file with this name already exists" in resp.text:
            for action in actions:
                action["action"] = "update"  # Change all actions to "update"
            body["actions"] = actions
            resp = self._http.post(url, headers=headers, json=body)

        if resp.status_code >= 400:
            raise UpstreamError(
                "GitLab commit failed",
                details={"status": resp.status_code, "body": _safe_json(resp)},
            )

        payload = resp.json()
        commit_id = payload.get("id") or payload.get("short_id")
        web_url = payload.get("web_url")
        log.info("git_commit_created", id=commit_id, url=web_url, file=file_path, branch=branch)
        return commit_id, web_url

    def create_project(
            self,
            project_name: str,
            *,
            namespace_path: str,
            visibility: str = "private",
            description: Optional[str] = None,
        ) -> dict:
        """
        Creates a new GitLab project or returns the existing project details if it already exists.
        
        Args:
            project_name (str): Name of the new project.
            namespace_path (str): GitLab namespace path (e.g., "group/subgroup").
            visibility (str): Project visibility ("private", "internal", "public").
            description (Optional[str]): Optional project description.
        
        Returns:
            dict: Project details containing the project ID and other metadata.
        """
        if not namespace_path or not project_name:
            raise ValidationError("namespace_path and project_name are required")

        log.info(f"namespace path : {namespace_path}")
        namespace_id = self.get_namespace_id_by_full_path(namespace_path)

        base_url = self._base_url
        headers = _auth_headers(self._pat)

        # Check if project already exists
        search_url = f"{base_url}/projects"
        search_params = {"search": project_name}
        search_resp = self._http.get(search_url, headers=headers, params=search_params)

        if search_resp.status_code >= 400:
            raise UpstreamError(
                "Failed to search for existing projects",
                details={"status": search_resp.status_code, "body": _safe_json(search_resp)},
            )

        existing_projects = search_resp.json()
        for project in existing_projects:
            if project.get("name") == project_name and project.get("namespace", {}).get("id") == namespace_id:
                log.info("git_project_exists", id=project.get("id"), name=project_name, url=project.get("web_url"))
                return {
                    "project_id": project.get("id"),
                    "name": project.get("name"),
                    "web_url": project.get("web_url"),
                    "namespace_path": namespace_path,
                }  # Return existing project details

        # Create project if it doesn't exist
        body = {
            "name": project_name,
            "visibility": visibility,
            "namespace_id": namespace_id
        }

        if description:
            body["description"] = description

        create_url = f"{base_url}/projects"
        create_resp = self._http.post(create_url, headers=headers, json=body)

        if create_resp.status_code >= 400:
            raise UpstreamError(
                "GitLab project creation failed",
                details={"status": create_resp.status_code, "body": _safe_json(create_resp)},
            )

        payload = create_resp.json()
        log.info("git_project_created", id=payload.get("id"), name=project_name, url=payload.get("web_url"))
        return {
            "project_id": payload.get("id"),
            "name": payload.get("name"),
            "web_url": payload.get("web_url"),
            "namespace_path": namespace_path,
        }  # Return newly created project details


    def get_namespace_id_by_full_path(self, namespace_path: str) -> int:
        """
        Searches for a namespace by its full path using the GitLab API and returns its ID.

        Args:
            host (str): GitLab host (e.g., "gitlab.example.com").
            namespace_path (str): Full namespace path (e.g., "group/subgroup/reflect").
            pat (str): Personal Access Token for authentication.

        Returns:
            int: Namespace ID if found.

        Raises:
            ValidationError: If the namespace is not found.
            UpstreamError: If the API call fails.
        """
        base_url = self._base_url
        headers = _auth_headers(self._pat)
        url = f"{base_url}/namespaces"
        params = {"search": namespace_path.split("/")[-1]}  # Use the last part of the path for fuzzy search

        resp = httpx.get(url, headers=headers, params=params)

        if resp.status_code >= 400:
            raise UpstreamError(
                "Failed to retrieve namespace ID",
                details={"status": resp.status_code, "body": _safe_json(resp)},
            )

        namespaces = resp.json()
        for namespace in namespaces:
            if namespace.get("full_path") == namespace_path:
                log.info("git_namespace_found", id=namespace.get("id"), path=namespace_path)
                return namespace.get("id")

        raise ValidationError(f"Namespace '{namespace_path}' not found on GitLab")


# ---------------- helpers ----------------

def _parse_gitlab_repo(repo_url: str) -> Tuple[str, str]:
    """
    Accepts repo URLs like:
      https://gitlab.example.com/group/subgroup/repo.git
    Returns:
      host -> "gitlab.example.com"
      project_id -> URL-encoded "group/subgroup/repo"
    """
    try:
        u = urlparse(repo_url)
        host = u.netloc
        path = (u.path or "").strip("/")
        if path.endswith(".git"):
            path = path[:-4]
        if not host or not path:
            raise ValueError("missing host/path")
        project_id = quote_plus(path)  # encode slashes as %2F
        return host, project_id
    except Exception as e:
        raise ValidationError("Invalid GitLab repo URL", details={"repo_url": repo_url, "error": str(e)})


def _auth_headers(pat: Optional[str]) -> dict:
    # Prefer PRIVATE-TOKEN header; Authorization: Bearer works on recent GitLab, too.
    h = {"Accept": "application/json"}
    if pat:
        h["PRIVATE-TOKEN"] = pat
    return h


def _guess_path_from_message(message: str) -> Optional[str]:
    """
    Parses "bootstrap <proxy>@rev<rev>" pattern to produce bundles/<proxy>/rev-<rev>.zip.
    """
    m = re.search(r"\bbootstrap\s+([a-zA-Z0-9\-_.]+)@rev(\d+)\b", message)
    if not m:
        return None
    proxy, rev = m.group(1), m.group(2)
    return f"bundles/{proxy}/rev-{rev}.zip"


def _safe_json(resp: httpx.Response):
    try:
        return resp.json()
    except Exception:
        return {"text": resp.text[:1000]}