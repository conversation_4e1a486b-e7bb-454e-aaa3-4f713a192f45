# Optimized Policy Catalog
# Single source of truth for policy metadata and parameter merge semantics
# Default values are maintained in: templates/*.xml.j2, system.yaml, and env.yaml

# Policy definitions with metadata and parameter merge semantics only
policies:
  # Security & Authentication Policies
  VerifyApiKey:
    template: "VerifyApiKey.xml.j2"
    name: "API Key Verification"
    description: "Verifies API key from request headers, query parameters, or form parameters"
    required_params: ["keyref"]
    optional_params: ["display_name"]
    param_config:
      keyref: {"type": "string", "merge": "replace"}
      display_name: {"type": "string", "merge": "replace"}

  OAuthV2:
    template: "OAuthV2.xml.j2"
    name: "OAuth 2.0 Verification"
    description: "Verifies OAuth 2.0 access tokens"
    required_params: []
    optional_params: ["token_ref", "scope", "remove_prefix", "display_name"]
    param_config:
      token_ref: {"type": "string", "merge": "replace"}
      scope: {"type": "string", "merge": "replace"}
      remove_prefix: {"type": "bool", "merge": "replace"}
      display_name: {"type": "string", "merge": "replace"}

  BasicAuthentication:
    template: "BasicAuthentication.xml.j2"
    name: "Basic Authentication"
    description: "Handles HTTP Basic Authentication encoding/decoding"
    required_params: []
    optional_params: ["operation", "source", "username_variable", "password_variable", "assign_to", "ignore_unresolved", "display_name"]
    param_config:
      operation: {"type": "string", "merge": "replace"}
      source: {"type": "string", "merge": "replace"}
      username_variable: {"type": "string", "merge": "replace"}
      password_variable: {"type": "string", "merge": "replace"}
      assign_to: {"type": "string", "merge": "replace"}
      ignore_unresolved: {"type": "bool", "merge": "replace"}
      display_name: {"type": "string", "merge": "replace"}

  # Traffic Management Policies
  SpikeArrest:
    template: "SpikeArrest.xml.j2"
    name: "Spike Arrest"
    description: "Protects against traffic spikes by smoothing request rates"
    required_params: ["rate"]
    optional_params: ["message_weight_ref", "identifier_ref", "display_name"]
    param_config:
      rate: {"type": "string", "merge": "replace", "validator": "is_valid_rate"}
      message_weight_ref: {"type": "string", "merge": "replace"}
      identifier_ref: {"type": "string", "merge": "replace"}
      display_name: {"type": "string", "merge": "replace"}

  Quota:
    template: "Quota.xml.j2"
    name: "Quota Management"
    description: "Enforces consumption limits on client apps"
    required_params: ["allow"]
    optional_params: ["interval", "timeunit", "identifier_ref", "distributed", "synchronous", "display_name"]
    param_config:
      allow: {"type": "string", "merge": "replace", "validator": "is_valid_rate"}
      interval: {"type": "string", "merge": "replace"}
      timeunit: {"type": "string", "merge": "replace"}
      identifier_ref: {"type": "string", "merge": "replace"}
      distributed: {"type": "bool", "merge": "replace"}
      synchronous: {"type": "bool", "merge": "replace"}
      display_name: {"type": "string", "merge": "replace"}

  # CORS & Messaging Policies
  CORS:
    template: "AssignMessage.CORS.xml.j2"
    name: "CORS Headers"
    description: "Handles Cross-Origin Resource Sharing (CORS) headers"
    required_params: []
    optional_params: ["allow_origins", "allow_methods", "allow_headers", "expose_headers", "max_age", "allow_credentials", "display_name"]
    param_config:
      allow_origins: {"type": "list", "merge": "replace", "normalize": "exact"}
      allow_methods: {"type": "list", "merge": "append_unique", "normalize": "exact"}
      allow_headers: {"type": "list", "merge": "append_unique", "normalize": "lower"}
      expose_headers: {"type": "list", "merge": "append_unique", "normalize": "exact"}
      max_age: {"type": "int", "merge": "replace"}
      allow_credentials: {"type": "bool", "merge": "replace"}
      display_name: {"type": "string", "merge": "replace"}

  # Threat Protection Policies
  JSONThreatProtection:
    template: "JSONThreatProtection.xml.j2"
    name: "JSON Threat Protection"
    description: "Protects against malicious JSON payloads"
    required_params: []
    optional_params: ["source", "max_container_depth", "max_array_elements", "max_object_members", "max_string_length", "max_number_length", "display_name"]
    param_config:
      source: {"type": "string", "merge": "replace"}
      max_container_depth: {"type": "int", "merge": "replace"}
      max_array_elements: {"type": "int", "merge": "replace"}
      max_object_members: {"type": "int", "merge": "replace"}
      max_string_length: {"type": "int", "merge": "replace"}
      max_number_length: {"type": "int", "merge": "replace"}
      display_name: {"type": "string", "merge": "replace"}

  XMLThreatProtection:
    template: "XMLThreatProtection.xml.j2"
    name: "XML Threat Protection"
    description: "Protects against malicious XML payloads"
    required_params: []
    optional_params: ["source", "max_depth", "max_attributes", "max_attribute_length", "max_children", "deny_doctype", "display_name"]
    param_config:
      source: {"type": "string", "merge": "replace"}
      max_depth: {"type": "int", "merge": "replace"}
      max_attributes: {"type": "int", "merge": "replace"}
      max_attribute_length: {"type": "int", "merge": "replace"}
      max_children: {"type": "int", "merge": "replace"}
      deny_doctype: {"type": "bool", "merge": "replace"}
      display_name: {"type": "string", "merge": "replace"}

  # Additional policies can be added here following the same pattern
  # ExtractVariables, AssignMessage, FlowCallout, etc.

# Policy groups - consolidates categories and templates into single grouping mechanism
# Groups can be used for UI organization, bulk operations, or pre-configured policy sets
groups:
  # Functional Categories (for UI organization)
  security:
    name: "Security & Authentication"
    description: "Policies for authentication, authorization, and security"
    icon: "shield"
    type: "category"
    policies:
      - VerifyApiKey
      - OAuthV2
      - BasicAuthentication

  traffic_management:
    name: "Traffic Management"
    description: "Policies for rate limiting, quotas, and traffic control"
    icon: "traffic-light"
    type: "category"
    policies:
      - SpikeArrest
      - Quota

  cors_messaging:
    name: "CORS & Messaging"
    description: "Cross-origin resource sharing and message handling"
    icon: "message-circle"
    type: "category"
    policies:
      - CORS

  threat_protection:
    name: "Threat Protection"
    description: "Policies for protecting against malicious requests"
    icon: "shield-alert"
    type: "category"
    policies:
      - JSONThreatProtection
      - XMLThreatProtection

  # Pre-configured Policy Sets (for common use cases)
  basic_security:
    name: "Basic API Security"
    description: "Standard security setup with API key verification and rate limiting"
    icon: "shield-check"
    type: "template"
    policies:
      - VerifyApiKey
      - SpikeArrest
      - Quota

  cors_enabled:
    name: "CORS-Enabled API"
    description: "API with CORS support for web applications"
    icon: "globe"
    type: "template"
    policies:
      - VerifyApiKey
      - CORS
      - SpikeArrest

  high_security:
    name: "High Security API"
    description: "Enhanced security with threat protection"
    icon: "shield-alert"
    type: "template"
    policies:
      - VerifyApiKey
      - JSONThreatProtection
      - XMLThreatProtection
      - SpikeArrest
      - Quota

# Note: Default parameter values are maintained in:
# 1. Template files (*.xml.j2) - Template fallback defaults
# 2. System configuration (systems/*.yaml) - System-wide defaults
# 3. Environment configuration (env/*/*.yaml) - Environment-specific overrides
# This eliminates duplication and provides clear precedence: TEMPLATE < SYSTEM < ENV < INPUT
