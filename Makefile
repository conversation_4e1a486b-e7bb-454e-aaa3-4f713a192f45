# Makefile for Apigee Proxy Factory
# ================================
# Development workflow automation

.PHONY: help install install-dev install-test clean test test-unit test-integration test-coverage lint format check run docs

# Default target
help:
	@echo "Apigee Proxy Factory - Development Commands"
	@echo "==========================================="
	@echo ""
	@echo "Setup Commands:"
	@echo "  install          Install production dependencies"
	@echo "  install-dev      Install development dependencies"
	@echo "  install-test     Install testing dependencies"
	@echo "  install-all      Install all dependencies"
	@echo ""
	@echo "Testing Commands:"
	@echo "  test             Run all tests"
	@echo "  test-unit        Run unit tests only"
	@echo "  test-integration Run integration tests only"
	@echo "  test-api         Run API tests only"
	@echo "  test-services    Run service tests only"
	@echo "  test-coverage    Run tests with coverage report"
	@echo "  test-performance Run performance tests"
	@echo "  test-phases      Run phase-specific tests"
	@echo ""
	@echo "Code Quality Commands:"
	@echo "  lint             Run all linting checks"
	@echo "  format           Format code with black and isort"
	@echo "  check            Run all quality checks"
	@echo "  security         Run security checks"
	@echo ""
	@echo "Development Commands:"
	@echo "  run              Start development server"
	@echo "  run-prod         Start production server"
	@echo "  clean            Clean up generated files"
	@echo "  docs             Generate documentation"
	@echo ""
	@echo "CI/CD Commands:"
	@echo "  ci-test          Run CI test suite"
	@echo "  ci-quality       Run CI quality checks"
	@echo "  ci-security      Run CI security checks"

# Installation targets
install:
	pip install -r requirements.txt

install-dev:
	pip install -r requirements-dev.txt

install-test:
	pip install -r requirements-test.txt

install-all:
	pip install -e .[all]

# Testing targets
test:
	python run_tests.py --all

test-unit:
	python run_tests.py --unit

test-integration:
	python run_tests.py --integration

test-api:
	python run_tests.py --api

test-services:
	python run_tests.py --services

test-coverage:
	python run_tests.py --coverage

test-performance:
	python run_tests.py --performance

test-phases:
	python run_tests.py --phases

# Alternative pytest commands
pytest-unit:
	pytest tests/unit/ -v

pytest-integration:
	pytest tests/ -m integration -v

pytest-coverage:
	pytest tests/ --cov=app --cov-report=html --cov-report=term-missing

# Code quality targets
lint:
	python run_tests.py --lint

format:
	black app/ tests/
	isort app/ tests/

check: lint
	mypy app/
	bandit -r app/

security:
	bandit -r app/
	safety check
	semgrep --config=auto app/

# Development targets
run:
	uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

run-prod:
	uvicorn app.main:app --host 0.0.0.0 --port 8000

clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf build/
	rm -rf dist/
	rm -rf htmlcov/
	rm -rf .coverage
	rm -rf .pytest_cache/
	rm -rf .mypy_cache/
	rm -rf test-results.xml
	rm -rf test-report.html
	rm -rf coverage.xml

docs:
	@echo "Generating documentation..."
	@echo "API documentation available at: http://localhost:8000/docs"
	@echo "Test coverage report: htmlcov/index.html"

# CI/CD targets
ci-test:
	pytest tests/ --cov=app --cov-report=xml --junit-xml=test-results.xml -v

ci-quality:
	flake8 app/ --max-line-length=120 --output-file=flake8-report.txt
	black --check app/
	isort --check-only app/
	mypy app/ --junit-xml=mypy-report.xml

ci-security:
	bandit -r app/ -f json -o bandit-report.json
	safety check --json --output safety-report.json

# Build targets
build:
	python setup.py sdist bdist_wheel

upload-test:
	twine upload --repository testpypi dist/*

upload:
	twine upload dist/*

# Docker targets (if needed)
docker-build:
	docker build -t apigee-proxy-factory .

docker-run:
	docker run -p 8000:8000 apigee-proxy-factory

# Environment setup
setup-dev: install-dev
	pre-commit install
	@echo "Development environment setup complete!"

setup-ci: install-test
	@echo "CI environment setup complete!"

# Quick development workflow
dev: clean format test-unit
	@echo "Development workflow complete!"

# Full validation workflow
validate: clean format lint test-coverage security
	@echo "Full validation complete!"
