<RaiseFault name="{{ name }}"{% if params.enabled is defined %} enabled="{{ 'true' if params.enabled else 'false' }}"{% endif %}{% if params.continueOnError is defined %} continueOnError="{{ 'true' if params.continueOnError else 'false' }}"{% endif %}{% if params.async is defined %} async="{{ 'true' if params.async else 'false' }}"{% endif %}>
  {% if params.assign_to is defined %}<AssignTo createNew="{{ 'true' if params.assign_to.createNew else 'false' }}">{{ params.assign_to.variable }}</AssignTo>{% endif %}
  <FaultResponse>
    <Set>
      {% if params.headers %}
      <Headers>
        {% for h in params.headers %}
        <Header name="{{ h.name }}">{{ h.value }}</Header>
        {% endfor %}
      </Headers>
      {% endif %}
      {% if params.payload is defined %}
      <Payload{% if params.content_type %} contentType="{{ params.content_type }}"{% endif %}>{{ params.payload }}</Payload>
      {% endif %}
      {% if params.status_code is defined %}<StatusCode>{{ params.status_code }}</StatusCode>{% endif %}
      {% if params.reason_phrase is defined %}<ReasonPhrase>{{ params.reason_phrase }}</ReasonPhrase>{% endif %}
    </Set>
  </FaultResponse>
</RaiseFault>