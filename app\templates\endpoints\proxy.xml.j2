<!-- ProxyEndpoint skeleton -->
<ProxyEndpoint name="{{ proxy_name | default('default') }}">
  <HTTPProxyConnection>
    <BasePath>{{ base_path | default('/') }}</BasePath>
    <VirtualHost>{{ virtual_host | default('default') }}</VirtualHost>
  </HTTPProxyConnection>

  <RouteRule name="{{ route_name | default('default') }}">
    <TargetEndpoint>{{ target_endpoint | default('default') }}</TargetEndpoint>
  </RouteRule>

  <PreFlow name="PreFlow">
    <Request>
      {% for p in (preflow_request or []) %}
      <Step><Name>{{ p }}</Name></Step>
      {% endfor %}
    </Request>
    <Response>
      {% for p in (preflow_response or []) %}
      <Step><Name>{{ p }}</Name></Step>
      {% endfor %}
    </Response>
  </PreFlow>

  <PostFlow name="PostFlow">
    <Request/>
    <Response/>
  </PostFlow>

  <PostClientFlow name="PostClientFlow">
    <Request/>
    <Response/>
  </PostClientFlow>
  <!-- You can inject <Flow> blocks here or let FlowBuilder PUT a full version later -->
  <Flows/>
</ProxyEndpoint>
